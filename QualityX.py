#!/usr/bin/env python3
"""
This script downloads financial data from Polygon.io API (/vX endpoint),
calculates quality metrics using available data points (attempting shares-based calcs),
fetches economic data from FRED, sends a prompt to a language model for qualitative analysis,
and saves the resulting report based on quantitative and qualitative scores.
"""

import os
import time
import json
import re
import requests
import concurrent.futures
from datetime import datetime
from dateutil.parser import parse
from dateutil.relativedelta import relativedelta
from dotenv import load_dotenv
from concurrent.futures import ThreadPoolExecutor
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Load environment variables from a .env file
load_dotenv()

# --- Configuration ---
class Config:
    COMPANIES = ["INTU"] # Example companies
    REQUEST_DELAY = 13  # Delay (seconds) for Polygon API (5 requests/min limit)
    INTER_COMPANY_DELAY = 13 # Delay between processing different companies sequentially
    MODEL_NAME = "anthropic/claude-sonnet-4" # Example LLM
    POLYGON_BASE_URL = "https://api.polygon.io"
    POLYGON_API_KEY  = os.getenv("POLYGON_API_KEY")
    MAX_RETRIES = 4 # Max retries for HTTP requests
    OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"
    OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
    MAX_WORKERS = 1 # Set to 1 for sequential processing (RECOMMENDED for free tier APIs)
    LLM_TIMEOUT = 120 # Timeout for LLM API calls (seconds)
    LLM_RETRIES = 3 # Retries for LLM calls
    LLM_RETRY_DELAY = 20 # Delay between LLM retries (seconds)
    DETAILED_LOGGING = False # Set to True for verbose calculation and API logging

    # Sector-Specific Adjustments Configuration
    ENABLE_SECTOR_ADJUSTMENTS = True # Set to False to disable sector-specific adjustments
    SECTOR_CACHE = {} # Cache for sector information to avoid repeated API calls

    # FRED Configuration
    FRED_API_KEY = os.getenv("FRED_API_KEY")
    FRED_BASE_URL = "https://api.stlouisfed.org/fred"
    FRED_SERIES_INFO = {
        "GDPC1": "Real Gross Domestic Product (Billions of Chained 2017 Dollars, SAAR)", # Quarterly
        "PCEC96": "Real Personal Consumption Expenditures (Billions of Chained 2017 Dollars, SAAR)", # Monthly
        "PCEPILFE": "Personal Consumption Expenditures Excluding Food and Energy (Chain-Type Price Index, 2017=100, SA)", # Monthly
        "UNRATE": "Unemployment Rate (Percent, SA)", # Monthly
        "FEDFUNDS": "Federal Funds Effective Rate (Percent, SA)", # Monthly
        "T10Y3M": "10-Year Treasury Constant Maturity Minus 3-Month Treasury Constant Maturity", # Daily
        "IPMAN": "Industrial Production: Manufacturing (NAICS)", # Monthly
        "DSPIC96": "Real Disposable Personal Income", # Monthly
        "AMTMNO": "Manufacturers' New Orders: Total Manufacturing" # Monthly
    }
    FRED_REQUEST_DELAY = 1 # Delay for FRED API calls (can be shorter than Polygon's)

# --- HTTP Session Setup ---
def setup_http_session():
    session = requests.Session()
    retries = Retry(total=Config.MAX_RETRIES, backoff_factor=1, status_forcelist=[429, 500, 502, 503, 504])
    adapter = HTTPAdapter(max_retries=retries)
    session.mount('https://', adapter)
    session.mount('http://', adapter)
    return session

# --- Rate-Limited API Request ---
def rate_limited_request(session, method, url, delay_override=None, **kwargs):
    actual_delay = Config.REQUEST_DELAY # Default to Polygon delay
    api_key_in_params = False

    if 'polygon.io' in url:
        if Config.POLYGON_API_KEY and 'apiKey' not in url: # Check if API key is already in URL string
            # Add apiKey to params if not in URL and key exists
            params = kwargs.get('params', {})
            if 'apiKey' not in params:
                params['apiKey'] = Config.POLYGON_API_KEY
                kwargs['params'] = params
            api_key_in_params = 'apiKey' in params
    elif 'api.stlouisfed.org' in url:
        actual_delay = Config.FRED_REQUEST_DELAY # Use FRED specific delay
        # FRED API key is expected to be in params by the calling function
        api_key_in_params = 'api_key' in kwargs.get('params', {})


    if delay_override is not None:
        actual_delay = delay_override

    if Config.DETAILED_LOGGING: print(f"Waiting {actual_delay}s before request to {url}...")
    time.sleep(actual_delay)

    if Config.DETAILED_LOGGING: print(f"Making request: {method} {url} with params {kwargs.get('params')}")
    response = session.request(method, url, **kwargs)

    if response.status_code == 429:
        print(f"WARNING: Rate limit hit (429) for {url}. Consider increasing request delay for this service.")
    response.raise_for_status() # Raises HTTPError for bad responses (4XX or 5XX)
    return response

# --- Helper: Get Financial Value ---
def get_financial_value(period_financials, statement_key, field_keys, default=None):
    if not period_financials or statement_key not in period_financials: return default
    statement_data = period_financials.get(statement_key)
    if not statement_data: return default
    for key in field_keys:
        item_data = statement_data.get(key)
        if item_data and isinstance(item_data, dict) and 'value' in item_data and item_data['value'] is not None:
            value = item_data['value']
            if isinstance(value, str) and (value.strip() == "" or value.isalpha()):
                if Config.DETAILED_LOGGING: print(f"Debug: Rejected non-numeric string value '{value}' for key '{key}'")
                continue
            return value
    return default

# --- Helper: Safe Division ---
def safe_divide(numerator, denominator, default=None):
    if numerator is None or denominator is None: return default
    try:
        num, den = float(numerator), float(denominator)
    except (ValueError, TypeError):
        if Config.DETAILED_LOGGING: print(f"Debug: Safe divide failed type/value conversion for {numerator} / {denominator}")
        return default
    return (num / den) if den != 0 else default

# --- Sector Detection and Mapping ---
def get_company_sector(ticker, session=None):
    """
    Get sector information for a company ticker using Polygon.io API.
    Returns standardized sector classification for quality metric adjustments.
    """
    if not Config.ENABLE_SECTOR_ADJUSTMENTS:
        return 'general'

    # Check cache first
    if ticker in Config.SECTOR_CACHE:
        return Config.SECTOR_CACHE[ticker]

    sector = 'general'  # Default sector
    try:
        if Config.POLYGON_API_KEY:
            if session is None:
                session = setup_http_session()

            # Use ticker details endpoint to get sector information
            url = f"{Config.POLYGON_BASE_URL}/v3/reference/tickers/{ticker}"
            resp = rate_limited_request(session, "GET", url)
            data = resp.json()

            if 'results' in data and data['results']:
                results = data['results']

                # Try to get sector from various fields
                raw_sector = None
                if 'sic_description' in results:
                    raw_sector = results['sic_description']
                elif 'sector' in results:
                    raw_sector = results['sector']
                elif 'industry' in results:
                    raw_sector = results['industry']

                if raw_sector:
                    sector = map_to_standard_sector(raw_sector, ticker)
                    if Config.DETAILED_LOGGING:
                        print(f"Debug [{ticker}]: Raw sector '{raw_sector}' mapped to '{sector}'")

    except Exception as e:
        if Config.DETAILED_LOGGING:
            print(f"Warning [{ticker}]: Could not fetch sector information: {e}")

    # Cache the result
    Config.SECTOR_CACHE[ticker] = sector
    return sector

def map_to_standard_sector(raw_sector, ticker):
    """
    Map raw sector/industry descriptions to standardized sectors for quality adjustments.
    """
    if not raw_sector:
        return 'general'

    raw_lower = raw_sector.lower()

    # REIT Detection
    if any(keyword in raw_lower for keyword in [
        'real estate', 'reit', 'property', 'realty', 'land', 'mortgage'
    ]):
        return 'reit'

    # Utilities Detection
    if any(keyword in raw_lower for keyword in [
        'electric', 'utility', 'utilities', 'power', 'gas', 'water', 'energy distribution'
    ]):
        return 'utility'

    # Healthcare/Biotech Detection (check before technology to catch biotechnology)
    if any(keyword in raw_lower for keyword in [
        'healthcare', 'pharmaceutical', 'biotech', 'medical', 'drug', 'health',
        'biotechnology', 'life sciences', 'therapeutics'
    ]):
        return 'healthcare'

    # Technology Detection
    if any(keyword in raw_lower for keyword in [
        'technology', 'software', 'computer', 'internet', 'semiconductor', 'tech',
        'information technology', 'telecommunications', 'electronic', 'digital'
    ]):
        return 'technology'

    # Financial Services Detection
    if any(keyword in raw_lower for keyword in [
        'bank', 'financial', 'insurance', 'investment', 'credit', 'finance',
        'securities', 'asset management', 'lending'
    ]):
        return 'financial'

    # Industrial Detection
    if any(keyword in raw_lower for keyword in [
        'industrial', 'manufacturing', 'machinery', 'aerospace', 'defense',
        'construction', 'materials', 'chemicals'
    ]):
        return 'industrial'

    return 'general'

# --- Sector-Specific Adjustment Functions ---
def apply_sector_adjustments_profitability(metrics, sector, ticker):
    """
    Apply sector-specific adjustments to profitability metrics.
    """
    adjusted_metrics = metrics.copy()

    if sector == 'reit':
        # REITs typically have lower ROE due to large asset/equity base
        # Average ROE for U.S. equity REITs is ~4.7%
        roe = adjusted_metrics.get('roe')
        if roe is not None:
            # Adjust ROE expectations: treat 5% as normal (no penalty)
            if Config.DETAILED_LOGGING:
                print(f"Debug [{ticker}]: REIT ROE adjustment - Original: {roe:.4f}")

    elif sector == 'utility':
        # Regulated utilities have authorized ROEs around 9.5-10%
        roe = adjusted_metrics.get('roe')
        if roe is not None:
            # Treat ROE ~9-10% as normal for utilities
            if Config.DETAILED_LOGGING:
                print(f"Debug [{ticker}]: Utility ROE adjustment - Original: {roe:.4f}")

    elif sector == 'technology':
        # Tech firms are R&D-intensive; consider R&D-adjusted metrics
        # Capitalizing R&D dramatically raises ROE
        if Config.DETAILED_LOGGING:
            print(f"Debug [{ticker}]: Technology sector - Consider R&D intensity in profitability")

    elif sector == 'healthcare':
        # Biotech/pharma have heavy R&D spend
        # Allow negative ROE due to R&D investments
        if Config.DETAILED_LOGGING:
            print(f"Debug [{ticker}]: Healthcare sector - R&D-intensive, allow negative ROE")

    return adjusted_metrics

def apply_sector_adjustments_payout(metrics, sector, ticker):
    """
    Apply sector-specific adjustments to payout efficiency metrics.
    """
    adjusted_metrics = metrics.copy()

    if sector == 'reit':
        # REITs required to distribute ≥90% of taxable income
        # High payout ratios (≥90%) are normal and required by law
        dividend_payout = adjusted_metrics.get('dividend_payout')
        if dividend_payout is not None and Config.DETAILED_LOGGING:
            print(f"Debug [{ticker}]: REIT payout adjustment - High payouts (≥90%) are normal")

    elif sector == 'utility':
        # Utilities historically payout ~60-65% of earnings
        # Allow high payouts (~60-70%) without penalty
        dividend_payout = adjusted_metrics.get('dividend_payout')
        if dividend_payout is not None and Config.DETAILED_LOGGING:
            print(f"Debug [{ticker}]: Utility payout adjustment - High payouts (60-70%) are normal")

    return adjusted_metrics

def apply_sector_adjustments_investment(metrics, sector, ticker):
    """
    Apply sector-specific adjustments to investment quality metrics.
    """
    adjusted_metrics = metrics.copy()

    if sector == 'technology' or sector == 'healthcare':
        # High-growth sectors often require high reinvestment
        # Allow high investment (capex/R&D) rates
        asset_growth = adjusted_metrics.get('asset_growth')
        investing_flow_efficiency = adjusted_metrics.get('investing_flow_efficiency')
        if Config.DETAILED_LOGGING:
            print(f"Debug [{ticker}]: {sector.title()} sector - Allow high investment rates")

    elif sector == 'industrial' or sector == 'utility':
        # Capital-intensive industries have low asset turnover
        # Expect low turnover (high asset base); no penalty
        asset_turnover = adjusted_metrics.get('asset_turnover')
        if asset_turnover is not None and Config.DETAILED_LOGGING:
            print(f"Debug [{ticker}]: {sector.title()} sector - Capital-intensive, expect low asset turnover")

    return adjusted_metrics

def apply_sector_adjustments_accounting(metrics, sector, ticker):
    """
    Apply sector-specific adjustments to accounting quality metrics.
    """
    adjusted_metrics = metrics.copy()

    if sector == 'financial':
        # Financial firms inherently use leverage
        # Emphasize cash-flow/debt or coverage, not raw D/E ratios
        if Config.DETAILED_LOGGING:
            print(f"Debug [{ticker}]: Financial sector - Focus on cash flow metrics over leverage ratios")

    return adjusted_metrics

# --- Fetch Dividends ---
def fetch_period_dividends_per_share(session, company, start_date, end_date):
    if not start_date or not end_date:
        print(f"Warning [{company}]: Cannot fetch dividends without period start/end dates.")
        return None

    base_dividends_url = f"{Config.POLYGON_BASE_URL}/v3/reference/dividends"
    params = {"ticker": company, "ex_dividend_date.gte": start_date, "ex_dividend_date.lte": end_date, "limit": 100, "dividend_type": "CD"}
    total_dividend_per_share = 0.0
    next_url = base_dividends_url
    page_count = 0

    try:
        if Config.DETAILED_LOGGING: print(f"Debug [{company}]: Fetching dividends between {start_date} and {end_date}...")
        while next_url:
            current_params = params.copy() if page_count == 0 else {} # Use full params only for the first request
            request_url = next_url

            # For paginated requests, Polygon's next_url might not include apiKey.
            # rate_limited_request handles adding apiKey to params if not in URL.
            # If apiKey is already in next_url, params should not also have it.
            if page_count > 0 and Config.POLYGON_API_KEY and 'apiKey=' in request_url and 'apiKey' in current_params:
                del current_params['apiKey']

            resp = rate_limited_request(session, "GET", request_url, params=current_params)
            data = resp.json()
            results = data.get('results', [])
            page_count += 1

            if not results and page_count == 1:
                if Config.DETAILED_LOGGING: print(f"Debug [{company}]: No cash dividends found in the specified period.")
                return 0.0

            for dividend in results:
                ex_date, cash_amount, div_type = dividend.get("ex_dividend_date"), dividend.get("cash_amount"), dividend.get("dividend_type")
                if ex_date and cash_amount is not None and div_type == 'CD':
                    try:
                        if start_date <= parse(ex_date).strftime('%Y-%m-%d') <= end_date:
                             total_dividend_per_share += float(cash_amount)
                    except (ValueError, TypeError):
                        print(f"Warning [{company}]: Could not parse dividend date '{ex_date}' or amount '{cash_amount}'.")

            next_url = data.get('next_url')
            # Ensure subsequent next_url calls will have apiKey handled by rate_limited_request or is already in URL
            if next_url and Config.POLYGON_API_KEY and 'apiKey=' not in next_url:
                # If we need to add it to the URL string itself for some reason (though params is preferred)
                # next_url += ('&' if '?' in next_url else '?') + f"apiKey={Config.POLYGON_API_KEY}"
                pass # rate_limited_request will handle adding it to params

        print(f"Info [{company}]: Fetched dividends across {page_count} page(s). Total DPS: {total_dividend_per_share:.4f}")
        return total_dividend_per_share
    except requests.exceptions.HTTPError as http_err:
         if http_err.response.status_code == 429: print(f"Warning [{company}]: Rate limit hit (429) fetching dividends.")
         else: print(f"Error [{company}]: HTTP error fetching dividends: {http_err}")
    except Exception as e: print(f"Error [{company}]: Exception fetching/processing dividends: {e}")
    return None

# --- Calculate Quality Metrics ---
def validate_financials(fin_data, ticker):
    """Check for data consistency and quality"""
    if not fin_data: return False

    # Check for balance sheet equation: Assets = Liabilities + Equity
    assets = get_financial_value(fin_data, 'balance_sheet', ['assets'])
    liabilities = get_financial_value(fin_data, 'balance_sheet', ['liabilities'])
    equity = get_financial_value(fin_data, 'balance_sheet', ['equity_attributable_to_parent', 'equity'])

    # If all values present, check balance
    if assets is not None and liabilities is not None and equity is not None:
        try:
            a, l, e = float(assets), float(liabilities), float(equity)
            # Allow for some rounding differences (within 5%)
            if abs((l + e) - a) / a > 0.05:
                print(f"Warning [{ticker}]: Balance sheet equation off by more than 5%")
                # Don't reject data, just warn
        except (ValueError, TypeError): pass

    # Check consistency of cash flow statements
    net_cash_op = get_financial_value(fin_data, 'cash_flow_statement',
        ['net_cash_flow_from_operating_activities_continuing', 'net_cash_flow_from_operating_activities'])
    net_cash_inv = get_financial_value(fin_data, 'cash_flow_statement',
        ['net_cash_flow_from_investing_activities_continuing', 'net_cash_flow_from_investing_activities'])
    net_cash_fin = get_financial_value(fin_data, 'cash_flow_statement',
        ['net_cash_flow_from_financing_activities_continuing', 'net_cash_flow_from_financing_activities'])

    # If all values present, check total cash flow
    if net_cash_op is not None and net_cash_inv is not None and net_cash_fin is not None:
        try:
            total_cf = float(net_cash_op) + float(net_cash_inv) + float(net_cash_fin)
            net_change_cash = get_financial_value(fin_data, 'cash_flow_statement', ['net_change_in_cash'])
            if net_change_cash is not None:
                if abs(total_cf - float(net_change_cash)) / abs(float(net_change_cash)) > 0.1:
                    print(f"Warning [{ticker}]: Cash flow components don't sum to net change in cash (>10% difference)")
        except (ValueError, TypeError): pass

    return True  # Still use data even if validation issues

def calculate_quality_metrics(financial_data, session):
    # Initialize key variables that might be referenced before definition
    ebitda = None
    company_ticker = financial_data.get('company', 'UNKNOWN')
    raw_financials = financial_data.get('financials', [])
    if not raw_financials or len(raw_financials) < 2:
        print(f"Error [{company_ticker}]: Insufficient financial periods (< 2) from /vX endpoint."); return None
    if not all(isinstance(p.get('financials'), dict) for p in raw_financials[:2]):
        print(f"Error [{company_ticker}]: 'financials' dictionary missing or invalid in period data."); return None

    # Get sector information for adjustments
    company_sector = get_company_sector(company_ticker, session)
    if Config.DETAILED_LOGGING:
        print(f"Debug [{company_ticker}]: Detected sector: {company_sector}")

    # Validate financial data quality
    latest_fin = raw_financials[0]['financials']
    if not validate_financials(latest_fin, company_ticker):
        print(f"Warning [{company_ticker}]: Financial data quality validation issues detected.")

    latest_p, prior_p = raw_financials[0], raw_financials[1]
    latest_fin, prior_fin = latest_p['financials'], prior_p['financials']
    start_date, end_date = latest_p.get("start_date"), latest_p.get("end_date")

    # Income Statement
    revenue = get_financial_value(latest_fin, 'income_statement', ['revenues'])
    net_income = get_financial_value(latest_fin, 'income_statement', ['net_income_loss_attributable_to_parent', 'net_income_loss'])
    op_income = get_financial_value(latest_fin, 'income_statement', ['operating_income_loss'])
    gross_profit = get_financial_value(latest_fin, 'income_statement', ['gross_profit'])
    interest_exp = get_financial_value(latest_fin, 'income_statement', ['interest_expense_operating'])
    shares_basic = get_financial_value(latest_fin, 'income_statement', ['basic_average_shares', 'weighted_average_shares_outstanding_basic'])
    shares_basic_prev = get_financial_value(prior_fin, 'income_statement', ['basic_average_shares', 'weighted_average_shares_outstanding_basic'])

    # Balance Sheet
    assets = get_financial_value(latest_fin, 'balance_sheet', ['assets'])
    liabilities = get_financial_value(latest_fin, 'balance_sheet', ['liabilities'])
    equity = get_financial_value(latest_fin, 'balance_sheet', ['equity_attributable_to_parent', 'equity'])
    assets_prev = get_financial_value(prior_fin, 'balance_sheet', ['assets'])

    # Additional Balance Sheet Items
    current_assets = get_financial_value(latest_fin, 'balance_sheet', ['current_assets'])
    current_liabilities = get_financial_value(latest_fin, 'balance_sheet', ['current_liabilities'])

    # Cash Flow Statement
    net_cash_op = get_financial_value(latest_fin, 'cash_flow_statement', ['net_cash_flow_from_operating_activities_continuing', 'net_cash_flow_from_operating_activities'])
    net_cash_inv = get_financial_value(latest_fin, 'cash_flow_statement', ['net_cash_flow_from_investing_activities_continuing', 'net_cash_flow_from_investing_activities'])



    # Add back the equity calculation from assets and liabilities if missing
    if equity is None and assets is not None and liabilities is not None:
        try:
            equity = float(assets) - float(liabilities)
            if Config.DETAILED_LOGGING:
                print(f"Debug [{company_ticker}]: Calculated equity from assets and liabilities: {equity}")
        except (ValueError, TypeError):
            equity = None

    # EBITDA calculation - define this variable early
    ebitda = get_financial_value(latest_fin, 'income_statement', ['ebitda'])

    # Safely convert net_cash_inv to float for capex_proxy

    # If EBITDA not directly available, calculate from operating income
    if ebitda is None and op_income is not None:
        depreciation_amort = get_financial_value(latest_fin, 'income_statement',
            ['depreciation_and_amortization', 'depreciation', 'amortization'])
        if depreciation_amort is not None:
            try:
                ebitda = float(op_income) + float(depreciation_amort)
            except (ValueError, TypeError):
                if Config.DETAILED_LOGGING: print(f"Debug [{company_ticker}]: Could not calculate EBITDA")

    # Better CAPEX tracking
    capex = get_financial_value(latest_fin, 'cash_flow_statement',
        ['capital_expenditure', 'payments_to_acquire_property_plant_equipment'])

    # Fall back to proxy if direct value not available
    capex_proxy = None
    if net_cash_inv is not None:
        try:
            capex_proxy = abs(float(net_cash_inv))
        except (ValueError, TypeError):
            if Config.DETAILED_LOGGING: print(f"Debug [{company_ticker}]: Could not convert net_cash_inv to float for capex_proxy")

    # Use direct capex if available, otherwise use proxy
    if capex is None:
        capex = capex_proxy

    dividends = None
    if company_ticker and start_date and end_date:
        dps = fetch_period_dividends_per_share(session, company_ticker, start_date, end_date)
        if dps is not None and shares_basic is not None:
            try:
                shares_num = float(shares_basic)
                if shares_num > 0: dividends = dps * shares_num
                elif dps == 0.0: dividends = 0.0
            except (ValueError, TypeError):
                if Config.DETAILED_LOGGING: print(f"Debug [{company_ticker}]: Could not convert shares_basic '{shares_basic}' to float for dividend calculation")
                # Keep dividends as None
        elif dps is None and Config.DETAILED_LOGGING: print(f"Debug [{company_ticker}]: Dividend fetch failed for total dividend calculation.")
        elif shares_basic is None and Config.DETAILED_LOGGING: print(f"Debug [{company_ticker}]: Shares Basic missing for total dividend calculation.")


    # Metric Calculations
    roe = safe_divide(net_income, equity)
    roa = safe_divide(net_income, assets)
    op_margin = safe_divide(op_income, revenue)
    gross_margin = safe_divide(gross_profit, revenue)



    # Working capital
    working_capital = None
    if current_assets is not None and current_liabilities is not None:
        try:
            working_capital = float(current_assets) - float(current_liabilities)
        except (ValueError, TypeError):
            if Config.DETAILED_LOGGING: print(f"Debug [{company_ticker}]: Could not calculate working capital")

    # Free cash flow
    fcf = None
    if net_cash_op is not None and capex is not None:
        try:
            fcf = float(net_cash_op) - float(capex)
        except (ValueError, TypeError):
            if Config.DETAILED_LOGGING: print(f"Debug [{company_ticker}]: Could not calculate free cash flow")

    # FCF to revenue ratio (no market cap in basic data)
    fcf_to_revenue = safe_divide(fcf, revenue)

    accruals_ratio = None
    if net_income is not None and net_cash_op is not None and assets is not None and assets_prev is not None:
        try:
            avg_assets = (float(assets) + float(assets_prev)) / 2
            accruals_ratio = safe_divide(float(net_income) - float(net_cash_op), avg_assets)
        except (ValueError, TypeError): pass

    earnings_quality = safe_divide(net_cash_op, net_income)
    div_payout = None
    if dividends is not None and net_income is not None:
        try:
            ni_num = float(net_income)
            if ni_num > 0: div_payout = safe_divide(dividends, ni_num)
        except (ValueError, TypeError): pass

    share_dilution = None
    if shares_basic is not None and shares_basic_prev is not None:
        try: share_dilution = safe_divide(float(shares_basic) - float(shares_basic_prev), float(shares_basic_prev))
        except (ValueError, TypeError): pass

    asset_growth = None
    if assets is not None and assets_prev is not None:
        try: asset_growth = safe_divide(float(assets) - float(assets_prev), float(assets_prev))
        except (ValueError, TypeError): pass

    inv_flow_eff = safe_divide(capex_proxy, revenue)
    inv_flow_rate = safe_divide(capex_proxy, net_cash_op)

    asset_turnover = None
    if revenue is not None and assets is not None and assets_prev is not None:
        try:
            avg_assets = (float(assets) + float(assets_prev)) / 2
            asset_turnover = safe_divide(revenue, avg_assets)
        except (ValueError, TypeError): pass



    # FCF to dividend ratio if both available
    fcf_to_dividend = None
    if fcf is not None and dividends is not None and dividends > 0:
        try:
            fcf_to_dividend = float(fcf) / float(dividends)
        except (ValueError, TypeError):
            pass

    # Create base metrics
    base_metrics = {
        "profitability": {"roe": roe, "roa": roa, "operating_margin": op_margin, "gross_margin": gross_margin},
        "accounting_quality": {"accruals_ratio": accruals_ratio, "earnings_quality": earnings_quality},
        "payout_efficiency": {"dividend_payout": div_payout, "share_dilution": share_dilution, "fcf_to_dividend": fcf_to_dividend},
        "investment_quality": {"asset_growth": asset_growth, "investing_flow_efficiency": inv_flow_eff, "investing_flow_rate": inv_flow_rate, "asset_turnover": asset_turnover, "fcf_to_revenue": fcf_to_revenue},
        "raw_values_latest": {
            "filing_date": latest_p.get("filing_date"), "period_end_date": end_date, "revenue": revenue, "net_income": net_income, "operating_income": op_income,
            "gross_profit": gross_profit, "total_assets": assets, "total_assets_prev": assets_prev, "equity": equity, "total_liabilities": liabilities,
            "interest_expense": interest_exp, "net_cash_op": net_cash_op, "net_cash_investing": net_cash_inv, "capex": capex, "capex_proxy": capex_proxy,
            "dividends": dividends, "shares_basic": shares_basic, "current_assets": current_assets, "current_liabilities": current_liabilities,
            "ebitda": ebitda, "working_capital": working_capital, "free_cash_flow": fcf
        }
    }

    # Apply sector-specific adjustments
    if Config.ENABLE_SECTOR_ADJUSTMENTS:
        base_metrics["profitability"] = apply_sector_adjustments_profitability(base_metrics["profitability"], company_sector, company_ticker)
        base_metrics["accounting_quality"] = apply_sector_adjustments_accounting(base_metrics["accounting_quality"], company_sector, company_ticker)
        base_metrics["payout_efficiency"] = apply_sector_adjustments_payout(base_metrics["payout_efficiency"], company_sector, company_ticker)
        base_metrics["investment_quality"] = apply_sector_adjustments_investment(base_metrics["investment_quality"], company_sector, company_ticker)

    # Add sector information to metrics
    base_metrics["sector_info"] = {
        "sector": company_sector,
        "adjustments_enabled": Config.ENABLE_SECTOR_ADJUSTMENTS
    }

    metrics = base_metrics
    # Round metrics after calculation
    for category_metrics in metrics.values():
        if isinstance(category_metrics, dict):
            for k, v in category_metrics.items():
                if isinstance(v, (float, int)) and k not in ["shares_basic", "dividends"]: # Avoid rounding shares/dividends here
                    if k in ['earnings_quality', 'asset_turnover']:
                        category_metrics[k] = round(v, 2) if v != float('inf') else float('inf')
                    else:
                        category_metrics[k] = round(v, 4)

    metrics["base_quality_score"] = calculate_composite_quality(metrics, company_ticker)
    return metrics

# --- Scoring Functions ---
def winsorize(val, lower=-float('inf'), upper=float('inf')):
    if val is None: return None
    try:
        f_val = float(val)
        if f_val == float('inf'): return upper
        if f_val == float('-inf'): return lower
        return max(min(f_val, upper), lower)
    except (ValueError, TypeError): return None

def score_profitability(m, t, sector=None):
    s, c = 0, 0

    # Get sector information if not provided
    if sector is None:
        sector = Config.SECTOR_CACHE.get(t, 'general')

    # ROE: Research shows top decile (15-20%+) significantly outperforms. High ROE cutoff ~15-20% for max score
    # Apply sector-specific adjustments
    roe = winsorize(m.get('roe'), -0.5, 1.0)
    if roe is not None:
        if sector == 'reit':
            # REITs: Average ROE ~4.7%, treat 5% as normal (no penalty)
            s += (40 if roe >= 0.08 else 30 if roe >= 0.05 else 15 if roe >= 0.03 else -5 if roe < 0 else 0)
        elif sector == 'utility':
            # Utilities: Authorized ROEs around 9.5-10%, treat 9-10% as normal
            # Adjust thresholds to be more appropriate for utility sector
            s += (40 if roe >= 0.10 else 30 if roe >= 0.07 else 20 if roe >= 0.05 else 10 if roe >= 0.03 else -10 if roe < 0 else 0)
        elif sector == 'healthcare':
            # Healthcare/Biotech: Allow negative ROE due to R&D investments
            s += (40 if roe >= 0.15 else 30 if roe >= 0.10 else 15 if roe >= 0.05 else 5 if roe >= 0 else 0)  # No penalty for negative
        else:
            # Standard evaluation for other sectors
            s += (40 if roe >= 0.15 else 30 if roe >= 0.10 else 15 if roe >= 0.05 else -10 if roe < 0 else 0)
        c += 1

    # Operating Margin: Top quintile (~15-20%+) shows highest returns. Reward materially positive margins
    om = winsorize(m.get('operating_margin'), -0.5, 1.0)
    if om is not None:
        s += (35 if om >= 0.15 else 25 if om >= 0.10 else 10 if om >= 0.05 else -10 if om < 0 else 0)
        c += 1

    # Gross Margin: Novy-Marx (2013) shows gross profit is powerful. Very high margins (50-60%+) are excellent
    gm = winsorize(m.get('gross_margin'), 0.0, 1.0)
    if gm is not None:
        s += (25 if gm >= 0.50 else 20 if gm >= 0.40 else 10 if gm >= 0.25 else 0 if gm >= 0 else -5)
        c += 1
    else:
        s -= 5  # Penalize missing gross margin data

    final = min(max(s, 0), 100) if c > 0 else 40
    if Config.DETAILED_LOGGING:
        print(f"Debug [{t}]: Profitability Score = {final} (Count={c}, ROE={roe}, OM={om}, GM={gm}, Sector={sector})")
    return final

def score_accounting(m, t):
    b, s, c = 60, 0, 0

    # Accruals Ratio: Sloan (1996) shows low accruals outperform by ~2% annually
    # Firms with accruals <0 (cash flow > earnings) score highest, large positive accruals score poorly
    ar = winsorize(m.get('accruals_ratio'), -0.5, 0.5)
    if ar is not None:
        s += (30 if ar < 0 else 20 if ar <= 0.02 else 10 if ar <= 0.05 else -10 if ar <= 0.15 else -20)
        c += 1
    else:
        s -= 5

    # Earnings Quality (CFO/Net Income): Conservative accounting with CFO > Net Income is preferred
    # Piotroski F-score uses CFO > Net Income as quality indicator
    eq = winsorize(m.get('earnings_quality'), -1.0, 3.0)
    if eq is not None:
        s += (30 if eq >= 1.2 else 20 if eq >= 1.0 else 5 if eq >= 0.8 else -15 if eq < 0.7 else 0)
        c += 1
    else:
        s -= 5

    final = max(min(b + s, 100), 0) if c > 0 else 40
    if Config.DETAILED_LOGGING: print(f"Debug [{t}]: Accounting Score = {final} (Count={c}, AR={ar}, EQ={eq})")
    return final

def score_payout(m, t, sector=None):
    b, s, c = 60, 0, 0; dp, sd = None, None

    # Get sector information if not provided
    if sector is None:
        sector = Config.SECTOR_CACHE.get(t, 'general')

    if m and isinstance(m, dict):
        # Dividend Payout Ratio: Research shows top-decile dividend yield portfolios outperform
        # Boudoukh et al. (2007) found ~0.23% extra monthly return for high dividend yield firms
        # Moderate-to-high payout ratios (30-60% range) are optimal, very high (>70-80%) unsustainable
        dp = winsorize(m.get('dividend_payout'), 0.0, 1.5)
        if dp is not None:
            c += 1
            # Adjust scoring based on sector
            if sector == 'reit':
                # REITs required to distribute ≥90% of taxable income, so high payouts are normal
                s += 35 if 0.60 <= dp <= 0.95 else 20 if 0.40 <= dp < 0.60 else 10 if dp > 0.95 else 5
            elif sector == 'utility':
                # Utilities historically payout ~60-65% of earnings, allow high payouts without penalty
                s += 35 if 0.40 <= dp <= 0.75 else 20 if 0.20 <= dp < 0.40 else -10 if dp > 0.85 else 5
            else:
                # Standard evaluation: reward moderate-to-high payout (top quintile by yield)
                s += 35 if 0.30 <= dp <= 0.60 else 20 if 0.15 <= dp < 0.30 else 5 if dp == 0 else -15 if dp > 0.75 else 0
        else:
            s -= 5  # Metric N/A

        # Share Dilution: Research shows low net issuance (buybacks) earned ~3.6% annual excess return
        # Reward firms with little or negative net issuance (net buybacks), penalize high issuance
        sd = winsorize(m.get('share_dilution'), -0.15, 0.15)
        if sd is not None:
            c += 1
            s += (25 if sd <= -0.02 else 15 if sd <= 0 else 5 if sd <= 0.02 else -15 if sd <= 0.05 else -25)
        else:
            s -= 5  # Metric N/A

        # Check FCF coverage for dividends if available
        fcf_to_div = m.get('fcf_to_dividend')
        if fcf_to_div is not None:
            c+=1
            fcf_to_div = winsorize(fcf_to_div, 0.0, 5.0)
            s += 20 if fcf_to_div > 1.5 else 10 if fcf_to_div > 1.0 else -15 if fcf_to_div < 0.7 else 0
    else: s = -10 # Category missing

    final = max(min(b + s, 100), 0) if c > 0 else 30
    if Config.DETAILED_LOGGING:
        print(f"Debug [{t}]: Payout Score = {final} (Count={c}, DP={dp}, SD={sd}, Sector={sector})")
    return final

def score_investment(m, t, sector=None):
    b, s, c = 50, 0, 0

    # Get sector information if not provided
    if sector is None:
        sector = Config.SECTOR_CACHE.get(t, 'general')

    # Asset Growth: Research shows lowest-investment decile (slow growth) earned ~3% annual excess return
    # Low asset growth (<5-10% per year) scores high, very high growth (>20-25%) scores low
    # Apply sector-specific adjustments
    ag = winsorize(m.get('asset_growth'), -0.1, 0.5)
    if ag is not None:
        if sector in ['technology', 'healthcare']:
            # High-growth sectors: allow higher asset growth rates
            s += (25 if ag <= 0.10 else 15 if ag <= 0.20 else 5 if ag <= 0.35 else -10 if ag <= 0.50 else -20)
        else:
            # Standard evaluation for other sectors
            s += (25 if ag <= 0.05 else 15 if ag <= 0.10 else 0 if ag <= 0.20 else -15 if ag <= 0.30 else -25)
        c += 1
    else:
        s -= 5

    # Capex/Revenue (investing_flow_efficiency): Low Capex/Sales (<5% of sales) indicates efficient allocation
    # High Capex/Sales (>15-20% of sales) suggests potential overinvestment
    ife = winsorize(m.get('investing_flow_efficiency'), 0.0, 0.3)
    if ife is not None:
        if sector in ['technology', 'healthcare']:
            # Allow higher investment rates for R&D-intensive sectors
            s += (15 if ife <= 0.08 else 5 if ife <= 0.15 else -5 if ife <= 0.25 else -10)
        else:
            # Standard evaluation
            s += (15 if ife <= 0.05 else 5 if ife <= 0.10 else -5 if ife <= 0.15 else -15)
        c += 1
    else:
        s -= 5

    # Investment Flow Rate: Moderate reinvestment is optimal
    ifr = winsorize(m.get('investing_flow_rate'), 0.0, 1.0)
    if ifr is not None:
        s += (10 if 0.15 <= ifr <= 0.40 else 5 if ifr <= 0.15 else -10 if ifr > 0.60 else 0)
        c += 1
    else:
        s -= 5

    # Asset Turnover: High turnover (>1.0 sales-to-assets) indicates efficient resource use
    # Top quartile asset turnover shows higher ROIC
    at = winsorize(m.get('asset_turnover'), 0.0, 3.0)
    if at is not None:
        if sector in ['industrial', 'utility']:
            # Capital-intensive industries: expect low turnover, no penalty
            s += (25 if at >= 1.0 else 20 if at >= 0.6 else 10 if at >= 0.4 else 0 if at >= 0.2 else -5)
        else:
            # Standard evaluation
            s += (25 if at >= 1.5 else 20 if at >= 1.0 else 10 if at >= 0.8 else -10 if at < 0.5 else 0)
        c += 1
    else:
        s -= 5

    final = max(min(b + s, 100), 0) if c > 0 else 30
    if Config.DETAILED_LOGGING:
        print(f"Debug [{t}]: Investment Score = {final} (Count={c}, AG={ag}, IFE={ife}, IFR={ifr}, AT={at}, Sector={sector})")
    return final



def calculate_composite_quality(metrics, ticker):
    if not metrics: print(f"Error [{ticker}]: Metrics dictionary empty for composite score."); return 0

    # Get sector information for scoring adjustments
    sector = metrics.get('sector_info', {}).get('sector', 'general')

    # Updated weights based on research insights and user requirements:
    # Profitability 35%, Investment Quality 25%, Accounting Quality 20%, Payout Efficiency 20%
    # Research shows profitability and investment metrics are strongest predictors of future returns
    weights = {'profitability': 0.35, 'investment_quality': 0.25, 'accounting_quality': 0.20, 'payout_efficiency': 0.20}
    scores, total_w, comp_raw = {}, 0, 0

    # Define scoring functions with sector support
    score_funcs = {
        'profitability': lambda m, t: score_profitability(m, t, sector),
        'accounting_quality': score_accounting,  # No sector adjustments yet
        'payout_efficiency': lambda m, t: score_payout(m, t, sector),
        'investment_quality': lambda m, t: score_investment(m, t, sector)
    }

    missing_cats = [cat for cat in score_funcs if cat not in metrics or not isinstance(metrics.get(cat), dict)]
    if missing_cats and Config.DETAILED_LOGGING: print(f"Debug [{ticker}]: Missing categories for composite: {missing_cats}")

    for cat, func in score_funcs.items():
        if cat in metrics and isinstance(metrics[cat], dict):
            cat_s = func(metrics[cat], ticker)
            scores[cat] = cat_s
            if cat_s is not None:
                total_w += weights[cat]
                comp_raw += cat_s * weights[cat]
            else:
                scores[cat] = 'N/A'
        else:
            scores[cat] = 'N/A'

    comp_norm = safe_divide(comp_raw, total_w, 0)
    final_comp = max(0, min(comp_norm, 100))
    if Config.DETAILED_LOGGING:
        print(f"Debug [{ticker}]: Composite Score = {final_comp:.2f} (Raw={comp_raw:.2f}, Norm={comp_norm:.2f}, WeightSum={total_w:.2f}), Sector={sector}, breakdown={scores}")
    return final_comp

# --- Fetch Financial Data (Profile & Financials) ---
def fetch_financial_data(session, company):
    data = {'company': company, 'profile': {}, 'financials': []}
    try:
        print(f"Info [{company}]: Fetching profile...")
        resp = rate_limited_request(session, "GET", f"{Config.POLYGON_BASE_URL}/v3/reference/tickers/{company}")
        profile_data = resp.json().get('results', {})
        if profile_data.get('ticker'): data['profile'] = profile_data; print(f"Info [{company}]: Fetched profile for {profile_data.get('name', company)}.")
        else: print(f"Warning [{company}]: Profile data empty/invalid for {company}.")
    except Exception as e: print(f"Error [{company}]: Fetching profile failed: {e}")

    try:
        print(f"Info [{company}]: Fetching financials...")
        params = {"ticker": company, "limit": 6, "sort": "filing_date"}
        resp = rate_limited_request(session, "GET", f"{Config.POLYGON_BASE_URL}/vX/reference/financials", params=params)
        raw = resp.json().get('results', [])
        if raw and isinstance(raw, list) and len(raw) > 0 and 'financials' in raw[0]:
            data['financials'] = raw; print(f"Info [{company}]: Fetched {len(raw)} financial periods.")
        else: print(f"Warning [{company}]: Financials data issue for {company}. Results: {str(raw)[:100]}")
    except Exception as e: print(f"Error [{company}]: Fetching financials failed: {e}")
    return data

# --- Fetch Qualitative Data (News) ---
def fetch_qualitative_data(session, company):
    data = {"news": []}
    params = {"ticker": company, "limit": 50, "order": "desc"}
    try:
        print(f"Info [{company}]: Fetching news...")
        resp = rate_limited_request(session, "GET", f"{Config.POLYGON_BASE_URL}/v2/reference/news", params=params)
        news_results = resp.json().get('results', [])
        filtered_news, count, max_items, days_limit = [], 0, 20, 180
        if not news_results: print(f"Info [{company}]: No news items from API."); return data
        for n in news_results:
            if count >= max_items: break
            pub_utc, title = n.get("published_utc"), n.get("title")
            if pub_utc and title:
                try:
                    # Parse the date with timezone awareness
                    pub_date = parse(pub_utc)
                    # Get current time with timezone awareness
                    now = datetime.now().astimezone()
                    # Calculate days difference properly
                    if (now - pub_date).days <= days_limit:
                        filtered_news.append({"title": title, "published_utc": pub_utc, "article_url": n.get("article_url"), "description": n.get("description"), "insights": n.get("insights", []), "keywords": n.get("keywords", [])})
                        count += 1
                except Exception as e: print(f"Warning [{company}]: News date parse error '{pub_utc}': {e}")
        data["news"] = filtered_news
        print(f"Info [{company}]: Fetched {len(filtered_news)} news items (last {days_limit} days, max {max_items}).")
    except Exception as e: print(f"Error [{company}]: Fetching news failed: {e}")
    return data

def validate_fred_data(series_id, observations, name):
    """Validate FRED data for completeness and quality"""
    if not observations:
        print(f"Warning [FRED]: No observations found for {name} ({series_id})")
        return False

    # Check date range - only if we have observations
    if len(observations) < 2:
        print(f"Warning [FRED]: Not enough observations for {name} ({series_id}) to validate date range")
        return False

    try:
        start_date = datetime.strptime(observations[-1]['date'], '%Y-%m-%d')
        end_date = datetime.strptime(observations[0]['date'], '%Y-%m-%d')
        date_range = (end_date - start_date).days
    except (KeyError, IndexError, ValueError) as e:
        print(f"Warning [FRED]: Error parsing dates for {name} ({series_id}): {e}")
        return False

    # Expected frequency and minimum observations
    expected_freq = {
        # Series ID: (expected frequency in days, min observations expected)
        "GDPC1": (90, 8),           # Quarterly - expect 8 observations in 2 years
        "PCEC96": (30, 24),        # Monthly - expect 24 observations in 2 years
        "PCEPILFE": (30, 24),      # Monthly - expect 24 observations in 2 years
        "UNRATE": (30, 24),        # Monthly - expect 24 observations in 2 years
        "FEDFUNDS": (30, 24),      # Monthly - expect 24 observations in 2 years
        "T10Y3M": (1, 500),        # Daily - expect ~500 trading days in 2 years
        "IPMAN": (30, 24),         # Monthly - expect 24 observations in 2 years
        "DSPIC96": (30, 24),       # Monthly - expect 24 observations in 2 years
        "AMTMNO": (30, 24)         # Monthly - expect 24 observations in 2 years
    }

    # Get expected frequency for this series
    freq_days, min_obs = expected_freq.get(series_id, (30, 12))  # Default monthly, 12 obs

    # Check if we have enough observations
    if len(observations) < min_obs:
        print(f"Warning [FRED]: Only {len(observations)} observations for {name} ({series_id}), expected at least {min_obs}")

    # Check if date range is reasonable (at least 80% of 2 years)
    expected_range = 2 * 365  # 2 years in days
    if date_range < 0.8 * expected_range:
        print(f"Warning [FRED]: Date range for {name} ({series_id}) is only {date_range} days, expected at least {int(0.8 * expected_range)} days")

    # Check for gaps in data (only for higher frequency data)
    if freq_days <= 30:  # For monthly or higher frequency
        dates = [datetime.strptime(obs['date'], '%Y-%m-%d') for obs in observations]
        for i in range(1, len(dates)):
            gap = (dates[i-1] - dates[i]).days
            if gap > freq_days * 2.5:  # Allow for some flexibility
                print(f"Warning [FRED]: Possible gap in {name} ({series_id}) data between {dates[i].strftime('%Y-%m-%d')} and {dates[i-1].strftime('%Y-%m-%d')} ({gap} days)")

    return True

def fetch_economic_data_fred(session):
    if not Config.FRED_API_KEY: print("Warning: FRED_API_KEY not set. Skipping FRED economic data."); return {}
    print("Info: Fetching U.S. economic indicators from FRED...")
    fred_data = {}
    # Use 2 years of data for analysis
    start_date_str = (datetime.now() - relativedelta(years=2)).strftime('%Y-%m-%d')

    # Track successful and failed series
    successful_series = []
    failed_series = []

    for series_id, name in Config.FRED_SERIES_INFO.items():
        params = {
            "series_id": series_id,
            "api_key": Config.FRED_API_KEY,
            "file_type": "json",
            "observation_start": start_date_str,
            "sort_order": "desc",
            "frequency": "" # Get all available frequencies
        }
        try:
            resp = rate_limited_request(session, "GET", f"{Config.FRED_BASE_URL}/series/observations", params=params, delay_override=Config.FRED_REQUEST_DELAY)
            # Filter out missing values and limit to display limit
            observations = [obs for obs in resp.json().get('observations', []) if obs.get('value') != "."]

            # Validate the data
            if observations and validate_fred_data(series_id, observations, name):
                # Limit to display limit after validation
                # observations = observations[:Config.FRED_OBSERVATIONS_DISPLAY_LIMIT] # User requested full period
                # Store with series ID as key for better reference
                fred_data[f"{name} ({series_id})"] = [{"date": o.get('date'), "value": float(o.get('value'))} for o in observations]
                successful_series.append(series_id)

                print(f"Info [FRED]: Successfully fetched {len(observations)} observations for {name} ({series_id}), from {observations[-1]['date']} to {observations[0]['date']}.")
            else:
                failed_series.append(series_id)
        except Exception as e:
            print(f"Error [FRED]: Fetching {name} ({series_id}) failed: {e}")
            failed_series.append(series_id)

    # Summary report
    if fred_data:
        print(f"Info: Successfully fetched data for {len(successful_series)}/{len(Config.FRED_SERIES_INFO)} FRED series covering 2-year period.")
        if failed_series:
            print(f"Warning: Failed to fetch data for {len(failed_series)} series: {', '.join(failed_series)}")
    else:
        print("Warning: No data fetched from FRED.")

    return fred_data

# --- Generate LLM Prompt ---
def generate_analysis_prompt(company, financial_data, quality_metrics, qualitative_data, fred_economic_data):
    profile = financial_data.get('profile', {})
    raw_fin_latest = quality_metrics.get('raw_values_latest', {}) if quality_metrics else {}
    company_name, sector, description, ticker = profile.get('name', company), profile.get('sic_description', 'N/A'), profile.get('description', 'N/A'), profile.get('ticker', company)

    profile_str = json.dumps(profile, indent=2, default=str) if profile else "N/A"
    news_str = json.dumps(qualitative_data.get('news', []), indent=2, default=str) if qualitative_data.get('news') else "N/A"
    raw_fin_str = json.dumps(raw_fin_latest, indent=2, default=str) if raw_fin_latest else "N/A"

    fred_parts = ["Recent U.S. Economic Indicators (Source: FRED, full 2-year dataset):"]
    if fred_economic_data:
        for name, obs_list in fred_economic_data.items():
            # Format values with appropriate decimal places based on magnitude
            formatted_obs = []
            for obs in obs_list:
                value = obs['value']
                if abs(value) < 0.1:
                    formatted_value = f"{value:.4f}"
                elif abs(value) < 10:
                    formatted_value = f"{value:.2f}"
                else:
                    formatted_value = f"{value:.1f}"
                formatted_obs.append(f"  - {obs['date']}: {formatted_value}")

            fred_parts.append(f"- {name}:\n" + "\n".join(formatted_obs))
    else:
        fred_parts.append("No FRED economic data available.")
    fred_str = "\n".join(fred_parts)

    current_date = datetime.now().strftime('%Y-%m-%d')
    data_for_analysis = f"Analysis Date: {current_date}\n\nCompany Profile:\n{profile_str}\n\nLatest Period Raw Financial Values (Polygon.io /vX):\n{raw_fin_str}\n\nRecent News Snippets:\n{news_str}\n\nU.S. Macroeconomic Context Data:\n{fred_str}"

    # Prompt structure (simplified for brevity, full prompt is used in actual call)
    # The actual prompt is long and detailed as provided by the user initially.
    # This is just a placeholder to indicate where data_for_analysis is used.
    prompt = f"""
Goal:
As a meticulous and methodical financial analyst operating in "ultrathinking mode," your task is to evaluate {company_name} ({ticker}) by rigorously applying first-principles thinking and the Thesis-Antithesis-Synthesis framework to each of the 10 specified quality factors. Your analysis must be deeply reasoned and avoid superficial observations.

Begin with a top-down analysis of the U.S. macroeconomic environment. Utilize the provided 'U.S. Macroeconomic Context Data' (from FRED), focusing primarily on the 10-Year Treasury Constant Maturity Minus 3-Month Treasury Constant Maturity (T10Y3M), Federal Funds Effective Rate (DFF), Personal Consumption Expenditures (PCE) Excluding Food and Energy (Chain-Type Price Index) (DPCCRV1Q225SBEA). Integrate your general knowledge regarding other critical factors such as the current state of global supply chains and relevant U.S. government policies. Other provided macroeconomic indicators should be treated as supplementary.

Following the macro assessment, analyze {company_name}'s future performance prospects within this established macroeconomic context. Use the company's provided raw financial data, recent news, and your own domain knowledge.

Provided Data for Analysis (supplementary to your knowledge):
{data_for_analysis}

COMPANY OVERVIEW:
Name: {company_name} ({ticker}), Sector: {sector}
Description: {description}

Instructions:

1.  ***U.S. Macroeconomic Environment Analysis (Top-Down):***
    *   **Assess Key Indicators:** Evaluate the current state and trajectory of T10Y3M, DFF, and DPCCRV1Q225SBEA.
    *   **Integrate Broader Factors:** Incorporate the influence of global supply chains and U.S. government policies.
    *   **Synthesize Impact:** Conclude with a summary of the overall macroeconomic climate (e.g., expansionary, contractionary, inflationary, recessionary risk) and articulate the *fundamental impacts* these conditions are likely to have on businesses operating in {company_name}'s sector and on {company_name} specifically.

2.  ***Analysis of 10 Quality Factors (Iterative Process for EACH Factor):***
    For each of the 10 quality factors listed in the <required_format>:
    *   **A. Contextual Link:** Briefly state how the macroeconomic environment (from step 1) specifically creates opportunities or challenges for {company_name} concerning *this particular factor*.
    *   **B. First-Principle Deconstruction:**
        *   Break down *this factor* as it applies to {company_name} into its most fundamental components and assumptions.
        *   Question these components by repeatedly asking "Why is this the case?" or "What is the underlying driver here?" using the provided company data, news, and your knowledge.
        *   Identify the core causal relationships and evidence supporting them, moving beyond surface-level observations or correlations.
    *   **C. Thesis-Antithesis-Synthesis Application:**
        *   i. **Thesis:** Based on your first-principle deconstruction, construct a clear, evidence-backed positive argument (the "thesis") for {company_name}'s strength or potential regarding *this factor*.
        *   ii. **Antithesis:** Challenge the thesis. Identify and articulate potential weaknesses, risks, counter-arguments, or alternative interpretations (the "antithesis") related to *this factor*, again grounded in your first-principle analysis, company data, and the macro context.
        *   iii. **Synthesis & Scoring:**
            *   Reconcile the thesis and antithesis to form a nuanced, balanced, and well-reasoned conclusion (the "synthesis") about {company_name}'s standing on *this factor*.
            *   Based *explicitly* on this synthesis, assign a "Buffett Score" from -100 (critically weak) to +100 (exceptionally strong). *Briefly justify the score based on the synthesis.*
    *   **D. Articulate Reasoning (Internal Monologue/Chain of Thought for Output):** While performing B and C, your output for each factor should implicitly show this reasoning process before stating the score.

3.  ***Strict Adherence to Output Format:***
    *   Produce *only* the output specified in <required_format>.
    *   Ensure the "Composite Analysis" is a concise summary (max 100 words) of the most critical insights derived from the factor-by-factor analysis, highlighting any recent changes or pivotal events influencing the overall qualitative assessment.
    *   The `llm_qualitative_score: [score]/100` line must be exact.

Output Format:
<required_format>
1. SUSTAINABLE COMPETITIVE ADVANTAGE (ECONOMIC MOAT):
   Buffett Score: [Score]
2. MANAGEMENT QUALITY & EFFECTIVENESS:
   Buffett Score: [Score]
3. INNOVATION CAPACITY & R&D EFFECTIVENESS:
   Buffett Score: [Score]
4. CUSTOMER SATISFACTION & LOYALTY:
   Buffett Score: [Score]
5. ORGANIZATIONAL STRENGTH & EMPLOYEE SATISFACTION:
   Buffett Score: [Score]
6. CORPORATE GOVERNANCE & ETHICS:
   Buffett Score: [Score]
7. ENVIRONMENTAL, SOCIAL, & GOVERNANCE (ESG) COMMITMENT:
   Buffett Score: [Score]
8. FINANCIAL REPORTING QUALITY & TRANSPARENCY:
   Buffett Score: [Score]
9. BUSINESS MODEL RESILIENCE & SCALABILITY:
   Buffett Score: [Score]
10. COMPETITIVE POSITION & PRICING POWER:
    Buffett Score: [Score]

[llm_qualitative_score]
   -Rating: [Average of the 10 scores, rounded to one decimal]
   llm_qualitative_score: [score]/100  <-- IMPORTANT: Include this exact line and exact format
Composite Analysis (limited to 100 words): [Summary of key insights, recent changes, and events]
</required_format>

<10_quality_factors>
[Guidance: Your detailed reasoning based on Instructions 2.A, 2.B, 2.C.i, 2.C.ii, 2.C.iii]
1.  SUSTAINABLE COMPETITIVE ADVANTAGE (ECONOMIC MOAT): Fundamental drivers protecting long-term profitability and market share.
    Key Aspects: Brand Strength & Recognition, Network Effects, Cost Advantages, High Switching Costs, Patents & Intellectual Property, Regulatory Barriers, Efficient Scale.

2.  MANAGEMENT QUALITY & EFFECTIVENESS: The capability, integrity, and alignment of the leadership team.
    Key Aspects: Proven Capital Allocation Skill, CEO Tenure & Relevant Experience, Strong Leadership Team Cohesion, Significant Insider Ownership/Alignment, Transparent & Long-Term Focused Executive Compensation Structure (avoiding subjective metrics).

3.  INNOVATION CAPACITY & R&D EFFECTIVENESS: Ability to generate future growth through innovation.
    Key Aspects: Efficient & Productive R&D Investment, Strong Patent/IP Portfolio, Adaptability & Speed to Market, Positive Innovation Geography/Cluster Effects.

4.  CUSTOMER SATISFACTION & LOYALTY: Strength of customer relationships driving retention and pricing power.
    Key Aspects: High Customer Satisfaction Scores (e.g., Net Promoter Score (NPS) and the American Customer Satisfaction Index (ACSI)) vs Peers, Low Customer Churn/High Retention Rates, Strong Brand Loyalty.

5.  ORGANIZATIONAL STRENGTH & EMPLOYEE SATISFACTION: The health of the company culture and workforce engagement.
    Key Aspects: High Employee Satisfaction & Morale, Low Employee Turnover/High Retention, Strong Company Culture, Investment in Human Capital.

6.  CORPORATE GOVERNANCE & ETHICS: Systems ensuring accountability, fairness, transparency, and ethical behavior.
    Key Aspects: Board Independence & Effectiveness, Strong Shareholder Rights, Transparent Reporting & Accountability, Clean Ethical Track Record/Minimal Scandals, Effective CEO Succession Planning.

7.  ENVIRONMENTAL, SOCIAL, & GOVERNANCE (ESG) COMMITMENT: Integration of sustainable and responsible practices into strategy.
    Key Aspects: Strong Environmental Stewardship, Positive Social Impact & Stakeholder Relations (incl. community, diversity), Robust Governance Practices, Integration of Material ESG Factors into Strategy.

8.  FINANCIAL REPORTING QUALITY & TRANSPARENCY: Clarity, reliability, and timeliness of financial disclosures.
    Key Aspects: Transparent & Understandable Reporting, Consistent & Persistent Earnings (less manipulation), Timely Recognition of Economic Events, Strong Cash Flow Conversion relative to Earnings.

9.  BUSINESS MODEL RESILIENCE & SCALABILITY: The inherent strengths and adaptability of how the company operates and generates revenue.
    Key Aspects: High Mix of Recurring Revenue, Inherent Scalability, Operational Simplicity/Transparency, Strong Supplier Relationships (as indicator of trust/stability), Low Customer Concentration.

10. COMPETITIVE POSITION & PRICING POWER: The company's standing relative to rivals and ability to command favorable pricing.
    Key Aspects: Positive Market Share Trends (relative to industry growth), Demonstrated Pricing Power, Favorable Industry Structure (less intense competition).
</10_quality_factors>

Analyze carefully, focusing on fundamental reasoning. Stick strictly to the provided data/format.
"""
    return prompt

# --- Get LLM Analysis ---
def get_llm_analysis(prompt, ticker):
    if not Config.OPENROUTER_API_KEY or not Config.OPENROUTER_URL:
        print(f"Warning [{ticker}]: LLM API key/URL missing. Skipping LLM analysis."); return "LLM analysis skipped."
    for attempt in range(Config.LLM_RETRIES):
        try:
            headers = {"Authorization": f"Bearer {Config.OPENROUTER_API_KEY}", "Content-Type": "application/json", "HTTP-Referer": "http://localhost", "X-Title": "QualityX"}
            # Max prompt length check (OpenRouter has limits, e.g. 200k tokens for some models, characters are a rough proxy)
            # A more robust solution would be token counting if specific model limits are critical.
            # For now, a character limit is a basic safeguard.
            # Typical token to char ratio is ~1:4. 100k chars ~ 25k tokens.
            # Max prompt length for Deepseek Coder R1 is 128k tokens.
            # Let's use a generous char limit, e.g., 400,000 chars for prompt.
            max_prompt_chars = 400000
            if len(prompt) > max_prompt_chars:
                print(f"Warning [{ticker}]: Prompt length ({len(prompt)} chars) exceeds limit ({max_prompt_chars}). Truncating.")
                # Truncate safely at a newline or space to avoid cutting in the middle of a UTF-8 sequence
                safe_end = max_prompt_chars
                while safe_end > max_prompt_chars - 100 and safe_end > 0:
                    if prompt[safe_end] in ['\n', ' ', '.', '!', '?']:
                        break
                    safe_end -= 1
                # If we couldn't find a safe breakpoint, use a slightly smaller limit
                if safe_end <= max_prompt_chars - 100:
                    safe_end = max_prompt_chars - 10
                prompt = prompt[:safe_end]

            payload = {"model": Config.MODEL_NAME, "messages": [{"role": "user", "content": prompt}], "max_tokens": 50000, "top_k": 20, "top_p": 0.9, "temperature": 0.1}
            print(f"Info [{ticker}]: Sending request to LLM (attempt {attempt+1}/{Config.LLM_RETRIES}). Model: {Config.MODEL_NAME}, Prompt length: {len(prompt)} chars.")
            # Set separate connect and read timeouts for better control
            connect_timeout = min(30, Config.LLM_TIMEOUT / 5)  # 30 seconds or 1/5 of total timeout
            read_timeout = Config.LLM_TIMEOUT - connect_timeout  # Remaining time for reading
            response = requests.post(Config.OPENROUTER_URL, headers=headers, json=payload,
                                   timeout=(connect_timeout, read_timeout))
            response.raise_for_status()
            data = response.json()

            if "error" in data: print(f"Error [{ticker}]: LLM API error: {data['error'].get('message', data['error'])}")
            elif data.get("choices") and data["choices"][0].get("message") and "content" in data["choices"][0]["message"]:
                analysis = data["choices"][0]["message"]["content"]
                finish_reason = data["choices"][0].get("finish_reason", "N/A")
                print(f"Info [{ticker}]: LLM Response Received (length: {len(analysis)} chars, finish_reason: {finish_reason})")
                if finish_reason == "length": print(f"Warning [{ticker}]: LLM response may be truncated.")
                
                # Check if response has expected format
                if "llm_qualitative_score:" not in analysis or "Buffett Score:" not in analysis:
                    print(f"Warning [{ticker}]: LLM response format unexpected. Will retry.")
                    # Don't return here, let it fall through to retry logic
                else:
                    return analysis.strip()
            else: print(f"Error [{ticker}]: LLM response structure unexpected: {str(data)[:200]}")
        except requests.exceptions.Timeout: print(f"Warning [{ticker}]: LLM request timed out (attempt {attempt+1}).")
        except requests.exceptions.RequestException as e: print(f"Error [{ticker}]: LLM request error (attempt {attempt+1}): {e}")
        except Exception as e: print(f"Error [{ticker}]: Unexpected error in LLM analysis (attempt {attempt+1}): {e}"); import traceback; traceback.print_exc()

        # Retry if we haven't exceeded the maximum number of attempts
        if attempt < Config.LLM_RETRIES - 1:
            print(f"Info [{ticker}]: Retrying LLM call in {Config.LLM_RETRY_DELAY}s...")
            time.sleep(Config.LLM_RETRY_DELAY)
    return "LLM analysis failed after multiple attempts."

# --- Parse LLM Score ---
def parse_qualitative_score(text, ticker):
    if not text or "LLM analysis failed" in text or "LLM analysis skipped" in text: return 0
    patterns = [r"llm_qualitative_score:\s*([\d.-]+)\s*/\s*100", r"-\s*Rating:\s*([\d.-]+)"]
    for i, p_str in enumerate(patterns):
        match = re.search(p_str, text, re.IGNORECASE | re.MULTILINE)
        if match:
            try:
                score = float(match.group(1))
                if -100 <= score <= 100: print(f"Info [{ticker}]: Parsed LLM Score{' (fallback)' if i > 0 else ''}: {score}"); return score
                else: print(f"Warning [{ticker}]: Parsed LLM score {score} out of range. Using 0.")
            except ValueError: print(f"Error [{ticker}]: Could not convert LLM score '{match.group(1)}'. Using 0.")
            return 0 # Return 0 if parsing fails or score out of range after a match
    print(f"Error [{ticker}]: Could not find LLM score line. Using 0.")
    if Config.DETAILED_LOGGING: print(f"--- LLM Response for {ticker} (score parsing failed) ---\n{text[:500]}...\n--- End LLM Response ---")
    return 0

# --- Process Single Company ---
def process_company(session, company, fred_data_global):
    qm, analysis, llm_s, base_s, final_s = None, "", 0, 0, 0
    try:
        print(f"\n--- Starting analysis for {company} ---")
        fin_data = fetch_financial_data(session, company)
        qual_data = fetch_qualitative_data(session, company)

        if not fin_data.get('financials') or len(fin_data['financials']) < 2:
            print(f"Analysis failed [{company}]: Insufficient financial data."); save_analysis(company, "Failed: Insufficient financial data.", {"quality_score": 0}); return False

        qm = calculate_quality_metrics(fin_data, session)
        if qm is None:
            print(f"Analysis failed [{company}]: Could not calculate quality metrics."); save_analysis(company, "Failed: Could not calculate metrics.", {"quality_score": 0}); return False

        base_s = qm.get('base_quality_score', 0)
        print(f"Info [{company}]: Calculated Base Quantitative Score: {base_s:.2f}")

        prompt = generate_analysis_prompt(company, fin_data, qm, qual_data, fred_data_global)
        if Config.DETAILED_LOGGING: print(f"\n--- PROMPT FOR {company} (first 500 chars) START ---\n{prompt[:500]}...\n--- PROMPT FOR {company} END ---\n")
        analysis = get_llm_analysis(prompt, company)

        llm_failed_skipped = "LLM analysis failed" in analysis or "LLM analysis skipped" in analysis or not analysis.strip()
        if llm_failed_skipped:
            print(f"Warning [{company}]: LLM analysis failed/skipped. Final score based 40% on Quant score only.")
            if "LLM analysis failed" not in analysis and "LLM analysis skipped" not in analysis: analysis += "\n\n*** LLM ANALYSIS FAILED/SKIPPED ***"
            llm_s = 0; final_s = base_s * 0.4
        else:
            llm_s = parse_qualitative_score(analysis, company)
            final_s = (base_s * 0.3) + (llm_s * 0.7)

        qm['llm_qualitative_score'] = llm_s
        qm['quality_score'] = round(max(0, min(final_s, 100)), 2)

        filename = save_analysis(company, analysis, qm)
        print(f"Success [{company}]: Analysis saved to {filename} (Final Score: {qm.get('quality_score', 'N/A')})")
        return True
    except Exception as e:
        print(f"--- Analysis failed for {company}: Unexpected error in process_company: {e} ---"); import traceback; traceback.print_exc()
        final_qm = qm if qm else {}
        final_qm['llm_qualitative_score'] = final_qm.get('llm_qualitative_score', llm_s)
        if 'quality_score' not in final_qm:
            b = final_qm.get('base_quality_score', 0); l = final_qm.get('llm_qualitative_score', 0)
            # Use the same logic as in the normal flow to determine if LLM analysis failed
            llm_failed_skipped = "LLM analysis failed" in analysis or "LLM analysis skipped" in analysis or not analysis.strip()
            f_guess = (b * 0.3) + (l * 0.7 if not llm_failed_skipped else 0)
            final_qm['quality_score'] = round(max(0, min(f_guess, 100)), 2)
        err_msg = f"Analysis failed: {e}\n\nLLM Output (if any):\n{analysis if analysis else 'N/A'}\n\nPartial metrics below:"
        save_analysis(company, err_msg, final_qm)
        return False

# --- Save Analysis Report ---
def save_analysis(company, analysis_text, quality_metrics):
    qm = quality_metrics if quality_metrics else {}
    final_score = qm.get('quality_score', 0)
    safe_company = re.sub(r'[\\/*?:"<>|]', "", company)
    filename = f"{safe_company}_{final_score:.2f}.txt"
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"Analysis Report for {company}\nGenerated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Final Quality Score: {final_score:.2f}/100 (Quant: 40%, Qual: 60%)\n{'='*80}\n\n")
            f.write(f"Qualitative Analysis (LLM Output):\n{'-'*35}\n{analysis_text if analysis_text else 'N/A'}\n\n{'='*80}\n")
            f.write(f"Quantitative Metrics Summary:\n{'-'*35}\n")
            f.write(f"*** Notes: Polygon.io /vX Financials. N/A if data missing/non-numeric. Accruals (CF Method). Investment (Inv CF proxy). Payout/Dilution attempted (often N/A). ***\n\n")

            f.write(f"Base Quantitative Score: {qm.get('base_quality_score', 'N/A'):.2f}\n" if qm.get('base_quality_score') is not None else "Base Quantitative Score: N/A\n")
            f.write(f"LLM Qualitative Score: {qm.get('llm_qualitative_score', 'N/A'):.2f}\n" if qm.get('llm_qualitative_score') is not None else "LLM Qualitative Score: N/A\n")
            f.write(f"{'-'*10}\n")

            cat_order = ['profitability', 'accounting_quality', 'payout_efficiency', 'investment_quality']
            metric_fmt_order = {
                "profitability": ['roe', 'roa', 'operating_margin', 'gross_margin'],
                "accounting_quality": ['accruals_ratio', 'earnings_quality'],
                "payout_efficiency": ['dividend_payout', 'share_dilution'],
                "investment_quality": ['asset_growth', 'asset_turnover', 'investing_flow_efficiency', 'investing_flow_rate']
            }
            percent_metrics = ['roe', 'roa', 'operating_margin', 'gross_margin', 'dividend_payout', 'share_dilution', 'asset_growth', 'investing_flow_efficiency', 'investing_flow_rate', 'accruals_ratio']

            for cat in cat_order:
                if cat in qm and isinstance(qm[cat], dict):
                    f.write(f"{cat.replace('_', ' ').title()}:\n")
                    for metric in metric_fmt_order.get(cat, list(qm[cat].keys())):
                        if metric in qm[cat]:
                            val = qm[cat][metric]; title = metric.replace('_', ' ').title(); note = ""
                            if cat == 'payout_efficiency' and val is None: note = " (N/A - Data Unavailable)"

                            if val is None: f.write(f"  {title}: N/A{note}\n")
                            elif val == float('inf'): f.write(f"  {title}: Inf{note}\n")
                            elif metric in percent_metrics: f.write(f"  {title}: {val:.2%}{note}\n")
                            elif isinstance(val, (float, int)): f.write(f"  {title}: {val:.2f}x{note}\n" if metric in ['earnings_quality', 'asset_turnover'] else f"  {title}: {val:.2f}{note}\n")
                            else: f.write(f"  {title}: {val}{note}\n") # Should not happen if metrics are numeric
                    f.write("\n")

            if 'raw_values_latest' in qm and isinstance(qm['raw_values_latest'], dict):
                 f.write(f"{'-'*10}\nLatest Period Raw Values (Polygon /vX):\n")
                 raw_vals = qm['raw_values_latest']
                 raw_order = ['filing_date', 'period_end_date', 'revenue', 'net_income', 'operating_income', 'gross_profit', 'total_assets', 'total_assets_prev', 'equity', 'total_liabilities', 'net_cash_op', 'net_cash_investing', 'capex_proxy', 'interest_expense', 'dividends', 'shares_basic']
                 for key in raw_order:
                     if key in raw_vals:
                         val = raw_vals[key]; title = key.replace('_', ' ').title()
                         if val is None: f.write(f"  {title}: N/A\n")
                         elif isinstance(val, str) and re.match(r'\d{4}-\d{2}-\d{2}', val): f.write(f"  {title}: {val}\n") # Date string
                         elif isinstance(val, (int, float)) or (isinstance(val, str) and val.replace('.', '', 1).replace('-', '', 1).isdigit()):
                             try:
                                 f_val = float(val)
                                 fmt_str = f"{f_val:,.0f}" if key in ['shares_basic', 'dividends'] or abs(f_val) >= 10000 else f"{f_val:,.2f}"
                             except (ValueError, TypeError):
                                 fmt_str = str(val)
                             f.write(f"  {title}: {fmt_str}\n")
                         else: f.write(f"  {title}: {val}\n") # Other string values
        return filename
    except Exception as e:
        print(f"Error saving analysis file {filename}: {e}"); import traceback; traceback.print_exc()
        return f"SAVE_ERROR_{safe_company}_{datetime.now().strftime('%Y%m%d%H%M%S')}.txt"

# --- Main Execution ---
def main():
    print("--- Script Starting ---")
    # Critical API Key Checks
    if not Config.POLYGON_API_KEY: print("CRITICAL ERROR: POLYGON_API_KEY not set. Exiting."); return
    # Warnings for optional keys
    if not Config.OPENROUTER_API_KEY: print("Warning: OPENROUTER_API_KEY not set. LLM analysis will be skipped.")
    if not Config.FRED_API_KEY: print("Warning: FRED_API_KEY not set. FRED economic data will not be fetched.")
    if not Config.COMPANIES: print("Warning: No companies in Config.COMPANIES. Exiting."); return

    start_time = time.time()
    session = setup_http_session()

    fred_data_global = fetch_economic_data_fred(session) # Fetch once globally

    unique_companies = sorted(list(set(Config.COMPANIES)))
    print(f"Starting analysis for {len(unique_companies)} companies: {', '.join(unique_companies)}")
    print(f"LLM: {Config.MODEL_NAME if Config.OPENROUTER_API_KEY else 'N/A (Key Missing)'}, Detailed Logging: {Config.DETAILED_LOGGING}")
    print(f"Delays: Polygon API: {Config.REQUEST_DELAY}s, FRED API: {Config.FRED_REQUEST_DELAY}s, Inter-Company: {Config.INTER_COMPANY_DELAY}s")
    print(f"Max Workers: {Config.MAX_WORKERS}")

    results = []
    if Config.MAX_WORKERS <= 1 or len(unique_companies) == 1:
        print("Running analyses sequentially...")
        for i, company in enumerate(unique_companies):
            results.append(process_company(session, company, fred_data_global))
            if i < len(unique_companies) - 1: time.sleep(Config.INTER_COMPANY_DELAY)
    else:
        print(f"Running analyses concurrently (Max Workers: {Config.MAX_WORKERS})...")
        if 'polygon.io' in Config.POLYGON_BASE_URL and Config.REQUEST_DELAY * Config.MAX_WORKERS < 12: # 5 req/min = 12s per req
             print("WARNING: Concurrency with low Polygon delay may cause rate limits. Ensure total requests/min < 5.")
        with ThreadPoolExecutor(max_workers=Config.MAX_WORKERS) as executor:
            futures = [executor.submit(process_company, session, company, fred_data_global) for company in unique_companies]
            for i, future in enumerate(futures):
                try:
                    # Add a reasonable timeout (e.g., 10 minutes per company)
                    timeout_seconds = 600  # 10 minutes
                    results.append(future.result(timeout=timeout_seconds))
                    print(f"--- Completed analysis {i+1}/{len(unique_companies)} ---")
                except concurrent.futures.TimeoutError:
                    print(f"Error in concurrent task for company {unique_companies[i]}: Timed out after {timeout_seconds} seconds")
                    results.append(False)
                except Exception as e:
                    print(f"Error in concurrent task for company {unique_companies[i]}: {e}")
                    results.append(False)

    total_time = time.time() - start_time
    successful = sum(1 for r in results if r is True)
    print(f"\n--- Analysis Complete ---")
    print(f"Successfully completed {successful}/{len(unique_companies)} analyses.")
    if successful < len(unique_companies): print(f"Failed analyses: {len(unique_companies) - successful}")
    print(f"Total execution time: {total_time:.2f} seconds.\n--- Script Finished ---")

if __name__ == "__main__":
    main()